# AWS Bedrock Integration Setup

This document explains how to set up and use AWS Bedrock AI integration in your Periodic Android app.

## Prerequisites

1. **AWS Account**: You need an active AWS account
2. **AWS Bedrock Access**: Request access to the models you want to use
3. **AWS Credentials**: Access Key ID and Secret Access Key

## AWS Setup Steps

### 1. Request Model Access in AWS Console

1. Go to AWS Bedrock console
2. Navigate to "Model access" in the left sidebar
3. Click "Modify model access"
4. Enable access to desired models (recommended: Amazon Titan Text Express)
5. Wait for approval (usually takes a few minutes)

### 2. Create IAM User and Access Keys

1. Go to AWS IAM console
2. Create a new user (e.g., "bedrock-api-user")
3. Attach policies:
   - `AmazonBedrockReadOnly`
   - Custom policy for model invocation:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeModel",
                "bedrock:InvokeModelWithResponseStream"
            ],
            "Resource": "arn:aws:bedrock:*::foundation-model/*"
        }
    ]
}
```

4. Create access keys for the user
5. Save both Access Key ID and Secret Access Key

## Android App Setup

### 1. Dependencies Added

The following dependencies have been added to your project:

- AWS Bedrock Runtime SDK
- AWS Core SDK
- AWS Auth SDK
- OkHttp for networking

### 2. Configuration

#### Option A: Using the Setup Screen (Recommended for Development)

1. Add the AWS setup screen to your navigation
2. Use `AwsSetupScreen` composable to configure credentials
3. The app will test the connection automatically

#### Option B: Programmatic Configuration

```kotlin
// Inject AwsCredentialManager
@Inject lateinit var credentialManager: AwsCredentialManager

// Set credentials
credentialManager.setCredentials(
    accessKeyId = "YOUR_ACCESS_KEY_ID",
    secretAccessKey = "YOUR_SECRET_ACCESS_KEY"
)
```

### 3. Using the AI Service

#### Basic Text Generation

```kotlin
@Inject lateinit var bedrockService: BedrockAiService

// Generate text with Amazon Titan
val result = bedrockService.generateTextWithTitan(
    prompt = "Write a helpful todo item for organizing my day",
    maxTokens = 100,
    temperature = 0.7f
)

result.onSuccess { generatedText ->
    // Use the generated text
    println("AI Response: $generatedText")
}.onFailure { error ->
    // Handle error
    println("Error: ${error.message}")
}
```

#### Enhanced Todo Features

```kotlin
@Inject lateinit var aiTodoService: AiEnhancedTodoService

// Generate todo suggestions
val suggestions = aiTodoService.generateTodoSuggestions("I need to prepare for my presentation")

// Enhance a todo description
val enhanced = aiTodoService.enhanceTodoDescription("Buy groceries")

// Generate schedule suggestions
val schedule = aiTodoService.generateScheduleSuggestion(listOf("Study", "Exercise", "Cook dinner"))
```

## Available Models

The integration supports these models:

- **Amazon Titan Text Express** (recommended for general use)
- **Amazon Titan Text Lite** (faster, lower cost)
- **Anthropic Claude 3 Haiku** (requires separate access request)
- **Anthropic Claude 3 Sonnet** (requires separate access request)

## Security Considerations

### For Development
- Credentials are stored in SharedPreferences
- Use the setup screen for easy configuration

### For Production
Consider implementing:
- Android Keystore for credential encryption
- Backend service to proxy AI requests
- Environment-specific configuration
- Credential rotation

## Error Handling

The service includes comprehensive error handling:

```kotlin
sealed class AiServiceError : Exception() {
    object NotConfigured : AiServiceError()
    object NetworkError : AiServiceError()
    object AuthenticationError : AiServiceError()
    object ModelNotAvailable : AiServiceError()
    data class ApiError(val code: String, override val message: String) : AiServiceError()
    data class UnknownError(override val message: String) : AiServiceError()
}
```

## Testing

Test your setup:

```kotlin
val testResult = bedrockService.testConnection()
if (testResult.isSuccess) {
    println("AWS Bedrock connection successful!")
} else {
    println("Connection failed: ${testResult.exceptionOrNull()?.message}")
}
```

## Cost Considerations

- Amazon Titan Text Express: ~$0.0008 per 1K input tokens, ~$0.0016 per 1K output tokens
- Monitor usage in AWS console
- Set up billing alerts
- Consider implementing request limits in your app

## Troubleshooting

### Common Issues

1. **"Credentials not configured"**
   - Ensure you've set up credentials using `AwsCredentialManager`

2. **"Model not available"**
   - Check that you've requested access to the model in AWS console
   - Verify the model ID is correct

3. **"Authentication failed"**
   - Verify your access keys are correct
   - Check IAM permissions

4. **Network errors**
   - Ensure internet connectivity
   - Check if your network allows AWS API calls

### Debug Logging

Enable debug logging to see detailed request/response information:

```kotlin
// Logs are automatically enabled in debug builds
// Check LogCat for "BedrockAiService" tag
```

## Next Steps

1. Set up your AWS credentials using the setup screen
2. Test the connection
3. Integrate AI features into your existing todo functionality
4. Consider adding more sophisticated prompts for better results
5. Implement proper error handling in your UI
