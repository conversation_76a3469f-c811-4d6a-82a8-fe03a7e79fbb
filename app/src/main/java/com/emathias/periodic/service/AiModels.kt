package com.emathias.periodic.service

/**
 * Data classes for AI service responses and configurations
 */

data class AiResponse(
    val text: String,
    val modelUsed: String,
    val tokensUsed: Int? = null,
    val processingTimeMs: Long? = null
)

data class AiGenerationConfig(
    val maxTokens: Int = 512,
    val temperature: Float = 0.7f,
    val topP: Float = 0.9f,
    val modelId: String = BedrockAiService.AMAZON_TITAN_TEXT_EXPRESS
)

enum class AiModel(val modelId: String, val displayName: String, val provider: String) {
    TITAN_EXPRESS(
        BedrockAiService.AMAZON_TITAN_TEXT_EXPRESS,
        "Titan Text Express",
        "Amazon"
    ),
    TITAN_LITE(
        BedrockAiService.AMAZON_TITAN_TEXT_LITE,
        "Titan Text Lite",
        "Amazon"
    ),
    NOVA_LITE(
        BedrockAiService.AMAZON_NOVA_LITE,
        "Nova Lite",
        "Amazon"
    ),
    CLAUDE_HAIKU(
        BedrockAiService.ANTHROPIC_CLAUDE_HAIKU,
        "Claude 3 Haiku",
        "Anthropic"
    ),
    CLAUDE_SONNET(
        BedrockAiService.ANTHROPIC_CLAUDE_SONNET,
        "Claude 3 Sonnet",
        "Anthropic"
    );

    companion object {
        fun fromModelId(modelId: String): AiModel? {
            return values().find { it.modelId == modelId }
        }
    }
}

sealed class AiServiceError : Exception() {
    object NotConfigured : AiServiceError()
    object NetworkError : AiServiceError()
    object AuthenticationError : AiServiceError()
    object ModelNotAvailable : AiServiceError()
    data class ApiError(val code: String, override val message: String) : AiServiceError()
    data class UnknownError(override val message: String) : AiServiceError()
}
