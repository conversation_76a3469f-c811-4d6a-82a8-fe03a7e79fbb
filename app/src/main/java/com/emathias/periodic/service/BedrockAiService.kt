package com.emathias.periodic.service

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import software.amazon.awssdk.core.SdkBytes
import software.amazon.awssdk.services.bedrockruntime.BedrockRuntimeClient
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelRequest
import software.amazon.awssdk.services.bedrockruntime.model.InvokeModelResponse
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class BedrockAiService @Inject constructor(
    private val bedrockClient: BedrockRuntimeClient
) {
    companion object {
        private const val TAG = "BedrockAiService"
        
        // Available model IDs
        const val AMAZON_TITAN_TEXT_EXPRESS = "amazon.titan-text-express-v1"
        const val AMAZON_TITAN_TEXT_LITE = "amazon.titan-text-lite-v1"
        const val ANTHROPIC_CLAUDE_HAIKU = "anthropic.claude-3-haiku-20240307-v1:0"
        const val ANTHROPIC_CLAUDE_SONNET = "anthropic.claude-3-sonnet-20240229-v1:0"
    }

    /**
     * Generate text using Amazon Titan Text model
     */
    suspend fun generateTextWithTitan(
        prompt: String,
        maxTokens: Int = 512,
        temperature: Float = 0.7f,
        topP: Float = 0.9f,
        modelId: String = AMAZON_TITAN_TEXT_EXPRESS
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            val requestBody = JSONObject().apply {
                put("inputText", prompt)
                put("textGenerationConfig", JSONObject().apply {
                    put("maxTokenCount", maxTokens)
                    put("temperature", temperature)
                    put("topP", topP)
                    put("stopSequences", emptyArray<String>())
                })
            }

            val response = invokeModel(modelId, requestBody.toString())
            val responseBody = JSONObject(response)
            val results = responseBody.getJSONArray("results")
            val outputText = results.getJSONObject(0).getString("outputText")
            
            Result.success(outputText.trim())
        } catch (e: Exception) {
            Log.e(TAG, "Error generating text with Titan", e)
            Result.failure(e)
        }
    }

    /**
     * Generate text using Anthropic Claude model
     */
    suspend fun generateTextWithClaude(
        prompt: String,
        maxTokens: Int = 512,
        temperature: Float = 0.7f,
        modelId: String = ANTHROPIC_CLAUDE_HAIKU
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            val requestBody = JSONObject().apply {
                put("anthropic_version", "bedrock-2023-05-31")
                put("max_tokens", maxTokens)
                put("temperature", temperature)
                put("messages", arrayOf(
                    JSONObject().apply {
                        put("role", "user")
                        put("content", prompt)
                    }
                ))
            }

            val response = invokeModel(modelId, requestBody.toString())
            val responseBody = JSONObject(response)
            val content = responseBody.getJSONArray("content")
            val text = content.getJSONObject(0).getString("text")
            
            Result.success(text.trim())
        } catch (e: Exception) {
            Log.e(TAG, "Error generating text with Claude", e)
            Result.failure(e)
        }
    }

    /**
     * Generic method to invoke any Bedrock model
     */
    private suspend fun invokeModel(modelId: String, requestBody: String): String = withContext(Dispatchers.IO) {
        Log.d(TAG, "Invoking model: $modelId")
        Log.d(TAG, "Request body: $requestBody")

        val request = InvokeModelRequest.builder()
            .modelId(modelId)
            .body(SdkBytes.fromUtf8String(requestBody))
            .contentType("application/json")
            .accept("application/json")
            .build()

        val response: InvokeModelResponse = bedrockClient.invokeModel(request)
        val responseBody = response.body().asUtf8String()
        
        Log.d(TAG, "Response: $responseBody")
        return@withContext responseBody
    }

    /**
     * Test connection to Bedrock service
     */
    suspend fun testConnection(): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            // Try a simple request to test the connection
            generateTextWithTitan("Hello", maxTokens = 10)
            Result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Connection test failed", e)
            Result.failure(e)
        }
    }
}
